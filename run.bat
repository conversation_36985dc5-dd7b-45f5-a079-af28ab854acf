@echo off
echo ========================================
echo      مجيب الأسئلة الذكي
echo ========================================
echo.
echo جاري فحص المتطلبات...
python -c "import flask; print('✓ Flask مثبت')" 2>nul || (echo "✗ Flask غير مثبت" && goto :install)
python -c "import requests; print('✓ Requests مثبت')" 2>nul || (echo "✗ Requests غير مثبت" && goto :install)

echo.
echo فحص المكتبات الاختيارية...
python -c "import pyperclip; print('✓ Pyperclip مثبت - مراقبة الحافظة متاحة')" 2>nul || echo "⚠ Pyperclip غير مثبت - مراقبة الحافظة غير متاحة"
python -c "import cv2, PIL, pytesseract; print('✓ مكتبات OCR مثبتة - تحليل الصور متاح')" 2>nul || echo "⚠ مكتبات OCR غير مثبتة - تحليل الصور غير متاح"

echo.
echo بدء التطبيق...
echo افتح المتصفح واذهب إلى: http://localhost:5000
echo.
python app.py
goto :end

:install
echo.
echo تثبيت المتطلبات الأساسية...
pip install flask requests werkzeug
echo.
echo لتثبيت جميع الميزات (اختياري):
echo pip install -r requirements.txt
echo.
pause
python app.py

:end
pause
