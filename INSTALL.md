# دليل التثبيت - مجيب الأسئلة الذكي

## المتطلبات الأساسية

### 1. Python 3.7+
تأكد من تثبيت Python 3.7 أو أحدث

### 2. المكتبات الأساسية
```bash
pip install -r requirements.txt
```

## التثبيت المتقدم (لمراقبة التطبيقات الأخرى)

### 3. تثبيت Tesseract OCR (اختياري)

#### على Windows:
1. حمل Tesseract من: https://github.com/UB-Mannheim/tesseract/wiki
2. ثبت البرنامج في المجلد الافتراضي
3. أضف مسار Tesseract إلى متغيرات البيئة:
   ```
   C:\Program Files\Tesseract-OCR
   ```

#### على Linux:
```bash
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-ara  # للغة العربية
```

#### على macOS:
```bash
brew install tesseract
brew install tesseract-lang  # للغات إضافية
```

### 4. تثبيت المكتبات الإضافية

إذا واجهت مشاكل في التثبيت، جرب تثبيت المكتبات واحدة تلو الأخرى:

```bash
pip install pyperclip
pip install opencv-python
pip install Pillow
pip install pytesseract
pip install numpy
```

## التشغيل

### الطريقة الأولى:
```bash
python app.py
```

### الطريقة الثانية (Windows):
```bash
run.bat
```

## استكشاف الأخطاء

### مشكلة: خطأ في استيراد المكتبات
**الحل:**
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

### مشكلة: Tesseract غير موجود
**الحل:**
- تأكد من تثبيت Tesseract
- أضف المسار إلى متغيرات البيئة
- أو عدل الكود ليشير إلى المسار الصحيح

### مشكلة: خطأ في مراقبة الحافظة
**الحل:**
- تأكد من أن التطبيق يعمل بصلاحيات كافية
- على Linux قد تحتاج إلى تثبيت: `sudo apt-get install xclip`

### مشكلة: لا يعمل OCR
**الحل:**
- تأكد من تثبيت Tesseract بشكل صحيح
- جرب تشغيل الأمر في Terminal: `tesseract --version`

## الميزات المتاحة

### ✅ بدون مكتبات إضافية:
- الإجابة على الأسئلة يدوياً
- واجهة الويب الأساسية
- النظام الاحتياطي للإجابة

### ✅ مع المكتبات الإضافية:
- مراقبة الحافظة التلقائية
- تحليل النص من الصور (OCR)
- التكامل مع التطبيقات الأخرى

## نصائح للاستخدام

1. **ابدأ بالميزات الأساسية** ثم أضف الميزات المتقدمة
2. **اختبر مراقبة الحافظة** على أسئلة بسيطة أولاً
3. **استخدم صفحة الاختبار** للتأكد من عمل كل شيء
4. **راجع ملف README.md** للمزيد من التفاصيل

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md
2. تأكد من تثبيت جميع المتطلبات
3. جرب إعادة تشغيل التطبيق
4. تحقق من رسائل الخطأ في Terminal
