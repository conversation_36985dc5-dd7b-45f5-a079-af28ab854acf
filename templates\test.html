<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار مجيب الأسئلة الذكي</h1>
        
        <div class="test-section">
            <h3>اختبار سؤال صح/خطأ</h3>
            <button onclick="testTrueFalse()">اختبار: الأرض كروية</button>
            <div id="trueFalseResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار سؤال متعدد الخيارات</h3>
            <button onclick="testMultipleChoice()">اختبار: عاصمة فرنسا</button>
            <div id="multipleChoiceResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h3>اختبار سؤال رياضي</h3>
            <button onclick="testMath()">اختبار: 2 + 2 = 4</button>
            <div id="mathResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <a href="/" style="text-decoration: none;">
                <button style="background: #2196F3;">العودة للصفحة الرئيسية</button>
            </a>
        </div>
    </div>

    <script>
        async function testQuestion(question, type, options = []) {
            try {
                // إرسال السؤال
                const response = await fetch('/submit_question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: question,
                        type: type,
                        options: options
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'submitted') {
                    // انتظار الإجابة
                    return await waitForAnswer(data.question_id);
                } else {
                    throw new Error('فشل في إرسال السؤال');
                }
                
            } catch (error) {
                return { status: 'error', message: error.message };
            }
        }

        async function waitForAnswer(questionId) {
            for (let i = 0; i < 12; i++) { // انتظار حتى 12 ثانية
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                try {
                    const response = await fetch(`/get_answer/${questionId}`);
                    const data = await response.json();
                    
                    if (data.status === 'completed') {
                        return data;
                    } else if (data.status === 'timeout' || data.status === 'error') {
                        return data;
                    }
                } catch (error) {
                    console.error('خطأ في التحقق من الإجابة:', error);
                }
            }
            
            return { status: 'timeout', message: 'انتهى الوقت' };
        }

        function displayResult(resultId, data) {
            const resultDiv = document.getElementById(resultId);
            resultDiv.style.display = 'block';
            
            if (data.status === 'completed') {
                resultDiv.className = 'result success';
                let answerText = '';
                if (data.type === 'true_false') {
                    answerText = data.answer ? 'صح' : 'خطأ';
                } else {
                    answerText = data.options[data.answer];
                }
                resultDiv.innerHTML = `<strong>الإجابة:</strong> ${answerText}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>خطأ:</strong> ${data.message || 'حدث خطأ غير متوقع'}`;
            }
        }

        async function testTrueFalse() {
            const button = event.target;
            button.disabled = true;
            button.textContent = 'جاري الاختبار...';
            
            const result = await testQuestion('الأرض كروية', 'true_false');
            displayResult('trueFalseResult', result);
            
            button.disabled = false;
            button.textContent = 'اختبار: الأرض كروية';
        }

        async function testMultipleChoice() {
            const button = event.target;
            button.disabled = true;
            button.textContent = 'جاري الاختبار...';
            
            const result = await testQuestion(
                'ما هي عاصمة فرنسا؟',
                'multiple_choice',
                ['لندن', 'باريس', 'برلين', 'روما']
            );
            displayResult('multipleChoiceResult', result);
            
            button.disabled = false;
            button.textContent = 'اختبار: عاصمة فرنسا';
        }

        async function testMath() {
            const button = event.target;
            button.disabled = true;
            button.textContent = 'جاري الاختبار...';
            
            const result = await testQuestion('2 + 2 = 4', 'true_false');
            displayResult('mathResult', result);
            
            button.disabled = false;
            button.textContent = 'اختبار: 2 + 2 = 4';
        }
    </script>
</body>
</html>
