#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال على كيفية التكامل مع تطبيق مجيب الأسئلة الذكي
يمكن لأي تطبيق آخر استخدام هذا الكود للحصول على إجابات تلقائية
"""

import requests
import json
import time

class QuestionAnswerClient:
    def __init__(self, server_url="http://localhost:5000"):
        self.server_url = server_url
        
    def ask_question(self, question_text, question_type="auto", options=None):
        """
        إرسال سؤال والحصول على الإجابة
        
        Args:
            question_text (str): نص السؤال
            question_type (str): نوع السؤال ("true_false", "multiple_choice", "auto")
            options (list): قائمة الخيارات للأسئلة متعددة الخيارات
            
        Returns:
            dict: الإجابة والمعلومات الإضافية
        """
        
        # تحديد نوع السؤال تلقائياً إذا لم يتم تحديده
        if question_type == "auto":
            if options and len(options) > 1:
                question_type = "multiple_choice"
            else:
                question_type = "true_false"
        
        # إرسال السؤال
        try:
            response = requests.post(
                f"{self.server_url}/submit_question",
                json={
                    "question": question_text,
                    "type": question_type,
                    "options": options or []
                },
                timeout=15
            )
            
            if response.status_code != 200:
                return {"error": f"خطأ في الخادم: {response.status_code}"}
            
            data = response.json()
            question_id = data.get("question_id")
            
            if not question_id:
                return {"error": "لم يتم الحصول على معرف السؤال"}
            
            # انتظار الإجابة
            return self._wait_for_answer(question_id)
            
        except requests.exceptions.RequestException as e:
            return {"error": f"خطأ في الاتصال: {str(e)}"}
    
    def _wait_for_answer(self, question_id, max_wait=12):
        """انتظار الحصول على الإجابة"""
        for i in range(max_wait):
            try:
                response = requests.get(f"{self.server_url}/get_answer/{question_id}")
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data.get("status") == "completed":
                        return {
                            "success": True,
                            "answer": data.get("answer"),
                            "question": data.get("question"),
                            "type": data.get("type"),
                            "options": data.get("options"),
                            "answer_text": self._format_answer(data)
                        }
                    elif data.get("status") in ["timeout", "error"]:
                        return {"error": data.get("message", "خطأ غير معروف")}
                
                time.sleep(1)
                
            except requests.exceptions.RequestException as e:
                return {"error": f"خطأ في التحقق من الإجابة: {str(e)}"}
        
        return {"error": "انتهى الوقت المحدد"}
    
    def _format_answer(self, data):
        """تنسيق الإجابة"""
        if data.get("type") == "true_false":
            return "صح" if data.get("answer") else "خطأ"
        elif data.get("options"):
            answer_index = data.get("answer", 0)
            options = data.get("options", [])
            if 0 <= answer_index < len(options):
                return options[answer_index]
        
        return str(data.get("answer", "غير معروف"))
    
    def process_external_text(self, text):
        """معالجة نص خارجي قد يحتوي على أسئلة"""
        try:
            response = requests.post(
                f"{self.server_url}/process_external_question",
                json={"text": text},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"خطأ في الخادم: {response.status_code}"}
                
        except requests.exceptions.RequestException as e:
            return {"error": f"خطأ في الاتصال: {str(e)}"}

def main():
    """مثال على الاستخدام"""
    client = QuestionAnswerClient()
    
    print("🤖 مثال على التكامل مع مجيب الأسئلة الذكي")
    print("=" * 50)
    
    # مثال 1: سؤال صح/خطأ
    print("\n1️⃣ اختبار سؤال صح/خطأ:")
    result = client.ask_question("الأرض كروية؟", "true_false")
    
    if result.get("success"):
        print(f"   السؤال: {result['question']}")
        print(f"   الإجابة: {result['answer_text']}")
    else:
        print(f"   خطأ: {result.get('error')}")
    
    # مثال 2: سؤال متعدد الخيارات
    print("\n2️⃣ اختبار سؤال متعدد الخيارات:")
    result = client.ask_question(
        "ما هي عاصمة فرنسا؟",
        "multiple_choice",
        ["لندن", "باريس", "برلين", "روما"]
    )
    
    if result.get("success"):
        print(f"   السؤال: {result['question']}")
        print(f"   الخيارات: {', '.join(result['options'])}")
        print(f"   الإجابة: {result['answer_text']}")
    else:
        print(f"   خطأ: {result.get('error')}")
    
    # مثال 3: معالجة نص خارجي
    print("\n3️⃣ اختبار معالجة نص خارجي:")
    external_text = """
    سؤال: هل الشمس نجم؟
    أ) صح
    ب) خطأ
    """
    
    result = client.process_external_text(external_text)
    
    if result.get("status") == "success":
        print(f"   النص المُحلل: {external_text.strip()}")
        print(f"   السؤال المُستخرج: {result['question']}")
        print(f"   الإجابة: {result['answer_text']}")
    else:
        print(f"   خطأ: {result.get('message')}")
    
    print("\n✅ انتهى الاختبار!")
    print("\n💡 يمكنك استخدام هذا الكود في تطبيقك لإرسال الأسئلة تلقائياً")

if __name__ == "__main__":
    main()
