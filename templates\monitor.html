<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقبة التطبيقات الأخرى</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .method-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
        }
        
        .method-card:hover {
            border-color: #FF6B6B;
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.2);
        }
        
        .method-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .method-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }
        
        .btn.stop {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        
        .btn.secondary {
            background: linear-gradient(45deg, #6c757d, #5a6268);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.inactive {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .instructions h4 {
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .instructions ol {
            margin-right: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        
        .test-area {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-area textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            resize: vertical;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .navigation {
            text-align: center;
            margin-top: 30px;
        }
        
        .navigation a {
            text-decoration: none;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 مراقبة التطبيقات الأخرى</h1>
            <p>اجعل التطبيق يجيب تلقائياً على أسئلة التطبيقات الأخرى</p>
        </div>
        
        <div class="content">
            <!-- مراقبة الحافظة -->
            <div class="method-card">
                <h3>📋 مراقبة الحافظة (Clipboard Monitoring)</h3>
                <p>يراقب التطبيق الحافظة تلقائياً، وعندما تنسخ سؤالاً من أي تطبيق آخر، سيجيب عليه تلقائياً وينسخ الإجابة إلى الحافظة.</p>
                
                <div class="instructions">
                    <h4>طريقة الاستخدام:</h4>
                    <ol>
                        <li>اضغط "بدء مراقبة الحافظة"</li>
                        <li>اذهب إلى التطبيق الآخر</li>
                        <li>انسخ السؤال (Ctrl+C)</li>
                        <li>ارجع إلى التطبيق الآخر والصق الإجابة (Ctrl+V)</li>
                    </ol>
                </div>
                
                <button id="startClipboard" class="btn">بدء مراقبة الحافظة</button>
                <button id="stopClipboard" class="btn stop">إيقاف المراقبة</button>
                
                <div id="clipboardStatus" class="status inactive">
                    الحالة: غير نشط
                </div>
            </div>
            
            <!-- اختبار النص -->
            <div class="method-card">
                <h3>🧪 اختبار تحليل النص</h3>
                <p>اختبر قدرة التطبيق على تحليل الأسئلة من النص المنسوخ.</p>
                
                <div class="test-area">
                    <h4>الصق النص هنا للاختبار:</h4>
                    <textarea id="testText" placeholder="الصق سؤالاً هنا لاختبار التحليل...

أمثلة:
- الأرض كروية؟ صح أم خطأ
- ما هي عاصمة فرنسا؟
A) لندن
B) باريس  
C) برلين
D) روما"></textarea>
                    <button id="testTextBtn" class="btn">اختبار التحليل</button>
                    
                    <div id="testResult" class="result"></div>
                </div>
            </div>
            
            <!-- تعليمات إضافية -->
            <div class="method-card">
                <h3>⚙️ إعدادات إضافية</h3>
                <p>طرق أخرى للتكامل مع التطبيقات الخارجية.</p>
                
                <div class="instructions">
                    <h4>طرق أخرى متاحة:</h4>
                    <ol>
                        <li><strong>API Endpoint:</strong> يمكن للتطبيقات الأخرى إرسال الأسئلة إلى <code>/process_external_question</code></li>
                        <li><strong>ملف مشترك:</strong> يمكن مراقبة ملف نصي للأسئلة الجديدة</li>
                        <li><strong>OCR:</strong> التقاط الشاشة وتحليل النص (يتطلب تثبيت Tesseract)</li>
                    </ol>
                </div>
                
                <button id="checkStatus" class="btn secondary">فحص حالة النظام</button>
            </div>
            
            <div class="navigation">
                <a href="/"><button class="btn secondary">الصفحة الرئيسية</button></a>
                <a href="/test"><button class="btn secondary">صفحة الاختبار</button></a>
            </div>
        </div>
    </div>

    <script>
        let monitoringActive = false;
        
        // عناصر DOM
        const startBtn = document.getElementById('startClipboard');
        const stopBtn = document.getElementById('stopClipboard');
        const statusDiv = document.getElementById('clipboardStatus');
        const testTextArea = document.getElementById('testText');
        const testBtn = document.getElementById('testTextBtn');
        const testResult = document.getElementById('testResult');
        const checkStatusBtn = document.getElementById('checkStatus');

        // بدء مراقبة الحافظة
        startBtn.addEventListener('click', async function() {
            try {
                const response = await fetch('/start_clipboard_monitor', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    monitoringActive = true;
                    updateStatus(true);
                    showNotification('تم بدء مراقبة الحافظة بنجاح!', 'success');
                } else {
                    showNotification('فشل في بدء المراقبة: ' + data.message, 'error');
                }
            } catch (error) {
                showNotification('خطأ في الاتصال: ' + error.message, 'error');
            }
        });

        // إيقاف مراقبة الحافظة
        stopBtn.addEventListener('click', async function() {
            try {
                const response = await fetch('/stop_clipboard_monitor', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    monitoringActive = false;
                    updateStatus(false);
                    showNotification('تم إيقاف مراقبة الحافظة', 'success');
                } else {
                    showNotification('فشل في إيقاف المراقبة: ' + data.message, 'error');
                }
            } catch (error) {
                showNotification('خطأ في الاتصال: ' + error.message, 'error');
            }
        });

        // اختبار تحليل النص
        testBtn.addEventListener('click', async function() {
            const text = testTextArea.value.trim();
            
            if (!text) {
                showTestResult('يرجى إدخال نص للاختبار', 'error');
                return;
            }
            
            try {
                const response = await fetch('/process_external_question', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({text: text})
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    let resultText = `
                        <strong>السؤال:</strong> ${data.question}<br>
                        <strong>النوع:</strong> ${data.type === 'true_false' ? 'صح/خطأ' : 'متعدد الخيارات'}<br>
                    `;
                    
                    if (data.options) {
                        resultText += `<strong>الخيارات:</strong> ${data.options.join(', ')}<br>`;
                    }
                    
                    resultText += `<strong>الإجابة:</strong> ${data.answer_text}`;
                    
                    showTestResult(resultText, 'success');
                } else {
                    showTestResult('خطأ: ' + data.message, 'error');
                }
            } catch (error) {
                showTestResult('خطأ في الاتصال: ' + error.message, 'error');
            }
        });

        // فحص حالة النظام
        checkStatusBtn.addEventListener('click', async function() {
            try {
                const response = await fetch('/monitor_status');
                const data = await response.json();

                updateStatus(data.clipboard_active);

                let message = `حالة النظام:\n`;
                message += `- مراقبة الحافظة: ${data.clipboard_active ? 'نشط' : 'غير نشط'}\n`;
                message += `- مكتبة الحافظة: ${data.clipboard_available ? 'متاحة' : 'غير مثبتة'}\n`;
                message += `- مكتبات OCR: ${data.ocr_available ? 'متاحة' : 'غير مثبتة'}\n`;

                if (data.last_clipboard) {
                    message += `\nآخر محتوى: ${data.last_clipboard}`;
                }

                if (!data.clipboard_available) {
                    message += `\n\nلتفعيل مراقبة الحافظة، ثبت: pip install pyperclip`;
                }

                if (!data.ocr_available) {
                    message += `\nلتفعيل OCR، ثبت: pip install opencv-python pillow pytesseract numpy`;
                }

                showNotification(message, data.clipboard_available ? 'success' : 'error');
            } catch (error) {
                showNotification('خطأ في فحص الحالة: ' + error.message, 'error');
            }
        });

        function updateStatus(active) {
            if (active) {
                statusDiv.className = 'status active';
                statusDiv.textContent = 'الحالة: نشط - يراقب الحافظة';
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                statusDiv.className = 'status inactive';
                statusDiv.textContent = 'الحالة: غير نشط';
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        function showNotification(message, type) {
            // يمكن تحسين هذا بإضافة نظام إشعارات أفضل
            alert(message);
        }

        function showTestResult(message, type) {
            testResult.style.display = 'block';
            testResult.className = `result ${type}`;
            testResult.innerHTML = message;
        }

        // فحص الحالة عند تحميل الصفحة
        window.addEventListener('load', function() {
            checkStatusBtn.click();
        });
    </script>
</body>
</html>
