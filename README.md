# مجيب الأسئلة الذكي - Flask App

تطبيق Flask بسيط يجيب على الأسئلة تلقائياً باستخدام الذكاء الاصطناعي المجاني خلال 10 ثوانٍ.

## المميزات

### الميزات الأساسية:
- ✅ إجابة على أسئلة صح/خطأ
- ✅ إجابة على أسئلة متعددة الخيارات
- ✅ مؤقت 10 ثوانٍ للإجابة
- ✅ واجهة مستخدم عربية جميلة
- ✅ استخدام الذكاء الاصطناعي المجاني (Hugging Face)
- ✅ نظام احتياطي ذكي للإجابة في حالة فشل API

### الميزات المتقدمة (تتطلب مكتبات إضافية):
- 🔥 **مراقبة الحافظة التلقائية** - ينسخ الأسئلة ويجيب عليها تلقائياً
- 🔥 **تحليل النص من الصور (OCR)** - يقرأ الأسئلة من الصور
- 🔥 **التكامل مع التطبيقات الأخرى** - API للتطبيقات الخارجية
- 🔥 **تحليل ذكي للأسئلة** - يتعرف على أنواع الأسئلة المختلفة

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. إعداد مفتاح API (اختياري)

للحصول على إجابات أفضل، يمكنك الحصول على مفتاح API مجاني من Hugging Face:

1. اذهب إلى [https://huggingface.co/](https://huggingface.co/)
2. أنشئ حساب مجاني
3. اذهب إلى Settings > Access Tokens
4. أنشئ token جديد
5. استبدل `hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` في ملف `app.py` بالمفتاح الخاص بك

**ملاحظة:** التطبيق يعمل بدون مفتاح API أيضاً باستخدام نظام الإجابة الاحتياطي.

### 3. تشغيل التطبيق

```bash
python app.py
```

### 4. فتح التطبيق

افتح المتصفح واذهب إلى: `http://localhost:5000`

## كيفية الاستخدام

### الصفحة الرئيسية (`/`)
- اختر نوع السؤال (صح/خطأ أو متعدد الخيارات)
- اكتب سؤالك
- إذا اخترت متعدد الخيارات، أدخل الخيارات
- اضغط "إرسال السؤال"
- انتظر الإجابة خلال 10 ثوانٍ

### صفحة الاختبار (`/test`)
- تحتوي على أمثلة جاهزة لاختبار التطبيق
- يمكنك اختبار أنواع مختلفة من الأسئلة

### صفحة مراقبة التطبيقات (`/monitor`)
- **مراقبة الحافظة**: ينسخ الأسئلة من أي تطبيق ويجيب عليها تلقائياً
- **اختبار تحليل النص**: لاختبار قدرة التطبيق على فهم الأسئلة
- **فحص حالة النظام**: للتأكد من توفر جميع الميزات

## 🔥 طرق الإجابة على أسئلة التطبيقات الأخرى

### 1. مراقبة الحافظة (الأسهل)
```
1. اذهب إلى /monitor
2. اضغط "بدء مراقبة الحافظة"
3. انسخ السؤال من أي تطبيق (Ctrl+C)
4. الصق الإجابة في التطبيق الآخر (Ctrl+V)
```

### 2. استخدام API للتطبيقات
```python
import requests

# إرسال سؤال
response = requests.post('http://localhost:5000/process_external_question',
                        json={'text': 'الأرض كروية؟ صح أم خطأ'})
print(response.json())
```

### 3. محاكي تطبيق الاختبار
```bash
python quiz_simulator.py
```
- تطبيق تجريبي يحاكي تطبيق اختبار حقيقي
- يمكن تفعيل الإجابة التلقائية بالذكاء الاصطناعي

### 4. اختبار التكامل
```bash
python test_integration.py
```
- يختبر جميع طرق التكامل مع التطبيق

## بنية المشروع

```
فلاسك/
├── app.py              # الملف الرئيسي للتطبيق
├── requirements.txt    # المكتبات المطلوبة
├── README.md          # ملف التوثيق
└── templates/         # قوالب HTML
    ├── index.html     # الصفحة الرئيسية
    └── test.html      # صفحة الاختبار
```

## كيف يعمل التطبيق

1. **إرسال السؤال**: المستخدم يرسل سؤال عبر الواجهة
2. **معالجة متوازية**: التطبيق يعالج السؤال في خيط منفصل
3. **استدعاء AI**: يرسل السؤال لـ Hugging Face API
4. **تحليل الإجابة**: يحلل رد الذكاء الاصطناعي
5. **إرجاع النتيجة**: يعرض الإجابة للمستخدم خلال 10 ثوانٍ

## نصائح للاستخدام

- اكتب الأسئلة بوضوح
- للأسئلة متعددة الخيارات، أدخل خيارين على الأقل
- التطبيق يدعم اللغة العربية والإنجليزية
- في حالة عدم وجود اتصال بالإنترنت، سيستخدم النظام الاحتياطي

## استكشاف الأخطاء

### مشكلة: التطبيق لا يعمل
- تأكد من تثبيت جميع المتطلبات: `pip install -r requirements.txt`
- تأكد من أن المنفذ 5000 غير مستخدم

### مشكلة: الإجابات غير دقيقة
- تأكد من إعداد مفتاح Hugging Face API
- جرب إعادة صياغة السؤال بطريقة أوضح

### مشكلة: انتهاء الوقت
- تحقق من اتصال الإنترنت
- النظام الاحتياطي سيعمل تلقائياً

## التطوير المستقبلي

- إضافة المزيد من مصادر الذكاء الاصطناعي
- تحسين دقة الإجابات
- إضافة قاعدة بيانات لحفظ الأسئلة والإجابات
- إضافة نظام تقييم للإجابات

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه مجاناً.
