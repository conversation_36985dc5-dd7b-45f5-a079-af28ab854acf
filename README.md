# مجيب الأسئلة الذكي - Flask App

تطبيق Flask بسيط يجيب على الأسئلة تلقائياً باستخدام الذكاء الاصطناعي المجاني خلال 10 ثوانٍ.

## المميزات

- ✅ إجابة على أسئلة صح/خطأ
- ✅ إجابة على أسئلة متعددة الخيارات  
- ✅ مؤقت 10 ثوانٍ للإجابة
- ✅ واجهة مستخدم عربية جميلة
- ✅ استخدام الذكاء الاصطناعي المجاني (Hugging Face)
- ✅ نظام احتياطي للإجابة في حالة فشل API

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. إعد<PERSON> مفتاح API (اختياري)

للحصول على إجابات أفضل، يمكنك الحصول على مفتاح API مجاني من Hugging Face:

1. اذهب إلى [https://huggingface.co/](https://huggingface.co/)
2. أنشئ حساب مجاني
3. اذهب إلى Settings > Access Tokens
4. أنشئ token جديد
5. استبدل `hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` في ملف `app.py` بالمفتاح الخاص بك

**ملاحظة:** التطبيق يعمل بدون مفتاح API أيضاً باستخدام نظام الإجابة الاحتياطي.

### 3. تشغيل التطبيق

```bash
python app.py
```

### 4. فتح التطبيق

افتح المتصفح واذهب إلى: `http://localhost:5000`

## كيفية الاستخدام

### الصفحة الرئيسية (`/`)
- اختر نوع السؤال (صح/خطأ أو متعدد الخيارات)
- اكتب سؤالك
- إذا اخترت متعدد الخيارات، أدخل الخيارات
- اضغط "إرسال السؤال"
- انتظر الإجابة خلال 10 ثوانٍ

### صفحة الاختبار (`/test`)
- تحتوي على أمثلة جاهزة لاختبار التطبيق
- يمكنك اختبار أنواع مختلفة من الأسئلة

## بنية المشروع

```
فلاسك/
├── app.py              # الملف الرئيسي للتطبيق
├── requirements.txt    # المكتبات المطلوبة
├── README.md          # ملف التوثيق
└── templates/         # قوالب HTML
    ├── index.html     # الصفحة الرئيسية
    └── test.html      # صفحة الاختبار
```

## كيف يعمل التطبيق

1. **إرسال السؤال**: المستخدم يرسل سؤال عبر الواجهة
2. **معالجة متوازية**: التطبيق يعالج السؤال في خيط منفصل
3. **استدعاء AI**: يرسل السؤال لـ Hugging Face API
4. **تحليل الإجابة**: يحلل رد الذكاء الاصطناعي
5. **إرجاع النتيجة**: يعرض الإجابة للمستخدم خلال 10 ثوانٍ

## نصائح للاستخدام

- اكتب الأسئلة بوضوح
- للأسئلة متعددة الخيارات، أدخل خيارين على الأقل
- التطبيق يدعم اللغة العربية والإنجليزية
- في حالة عدم وجود اتصال بالإنترنت، سيستخدم النظام الاحتياطي

## استكشاف الأخطاء

### مشكلة: التطبيق لا يعمل
- تأكد من تثبيت جميع المتطلبات: `pip install -r requirements.txt`
- تأكد من أن المنفذ 5000 غير مستخدم

### مشكلة: الإجابات غير دقيقة
- تأكد من إعداد مفتاح Hugging Face API
- جرب إعادة صياغة السؤال بطريقة أوضح

### مشكلة: انتهاء الوقت
- تحقق من اتصال الإنترنت
- النظام الاحتياطي سيعمل تلقائياً

## التطوير المستقبلي

- إضافة المزيد من مصادر الذكاء الاصطناعي
- تحسين دقة الإجابات
- إضافة قاعدة بيانات لحفظ الأسئلة والإجابات
- إضافة نظام تقييم للإجابات

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه مجاناً.
