<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مجيب الأسئلة الذكي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }
        
        input[type="text"], textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .options-container {
            display: none;
        }
        
        .option-input {
            margin-bottom: 10px;
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .timer {
            display: none;
            text-align: center;
            margin: 20px 0;
            font-size: 1.5em;
            color: #ff6b6b;
        }
        
        .result {
            display: none;
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 مجيب الأسئلة الذكي</h1>
            <p>أدخل سؤالك وسأجيب عليه خلال 10 ثوانٍ باستخدام الذكاء الاصطناعي</p>
            <div style="margin-top: 15px;">
                <a href="/monitor" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 8px 15px; border-radius: 20px; margin: 0 5px;">🔍 مراقبة التطبيقات</a>
                <a href="/test" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 8px 15px; border-radius: 20px; margin: 0 5px;">🧪 اختبار</a>
            </div>
        </div>
        
        <div class="content">
            <form id="questionForm">
                <div class="form-group">
                    <label for="questionType">نوع السؤال:</label>
                    <select id="questionType" name="questionType">
                        <option value="true_false">صح أو خطأ</option>
                        <option value="multiple_choice">اختيار متعدد</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="questionText">السؤال:</label>
                    <textarea id="questionText" name="questionText" placeholder="اكتب سؤالك هنا..." required></textarea>
                </div>
                
                <div class="form-group options-container" id="optionsContainer">
                    <label>الخيارات:</label>
                    <div class="option-input">
                        <input type="text" placeholder="الخيار الأول" class="option">
                    </div>
                    <div class="option-input">
                        <input type="text" placeholder="الخيار الثاني" class="option">
                    </div>
                    <div class="option-input">
                        <input type="text" placeholder="الخيار الثالث" class="option">
                    </div>
                    <div class="option-input">
                        <input type="text" placeholder="الخيار الرابع" class="option">
                    </div>
                </div>
                
                <button type="submit" class="btn">إرسال السؤال</button>
            </form>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جاري معالجة السؤال...</p>
            </div>
            
            <div class="timer" id="timer">
                <p>الوقت المتبقي: <span id="timeLeft">10</span> ثانية</p>
            </div>
            
            <div class="result" id="result"></div>
        </div>
    </div>

    <script>
        const questionForm = document.getElementById('questionForm');
        const questionType = document.getElementById('questionType');
        const optionsContainer = document.getElementById('optionsContainer');
        const loading = document.getElementById('loading');
        const timer = document.getElementById('timer');
        const result = document.getElementById('result');
        const timeLeft = document.getElementById('timeLeft');

        // إظهار/إخفاء خيارات الأسئلة متعددة الخيارات
        questionType.addEventListener('change', function() {
            if (this.value === 'multiple_choice') {
                optionsContainer.style.display = 'block';
            } else {
                optionsContainer.style.display = 'none';
            }
        });

        // إرسال السؤال
        questionForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const questionText = document.getElementById('questionText').value;
            const type = questionType.value;
            let options = [];
            
            if (type === 'multiple_choice') {
                const optionInputs = document.querySelectorAll('.option');
                options = Array.from(optionInputs)
                    .map(input => input.value.trim())
                    .filter(option => option !== '');
                
                if (options.length < 2) {
                    alert('يجب إدخال خيارين على الأقل');
                    return;
                }
            }
            
            // إخفاء النموذج وإظهار التحميل
            questionForm.style.display = 'none';
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                // إرسال السؤال
                const response = await fetch('/submit_question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: questionText,
                        type: type,
                        options: options
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'submitted') {
                    // بدء المؤقت
                    startTimer(data.question_id);
                } else {
                    throw new Error('فشل في إرسال السؤال');
                }
                
            } catch (error) {
                showError('حدث خطأ في إرسال السؤال: ' + error.message);
            }
        });

        function startTimer(questionId) {
            loading.style.display = 'none';
            timer.style.display = 'block';
            
            let seconds = 10;
            timeLeft.textContent = seconds;
            
            const interval = setInterval(async () => {
                seconds--;
                timeLeft.textContent = seconds;
                
                // التحقق من الإجابة
                try {
                    const response = await fetch(`/get_answer/${questionId}`);
                    const data = await response.json();
                    
                    if (data.status === 'completed') {
                        clearInterval(interval);
                        showAnswer(data);
                        return;
                    } else if (data.status === 'timeout' || seconds <= 0) {
                        clearInterval(interval);
                        showError('انتهى الوقت المحدد');
                        return;
                    } else if (data.status === 'error') {
                        clearInterval(interval);
                        showError('حدث خطأ في معالجة السؤال');
                        return;
                    }
                } catch (error) {
                    console.error('خطأ في التحقق من الإجابة:', error);
                }
            }, 1000);
        }

        function showAnswer(data) {
            timer.style.display = 'none';
            result.style.display = 'block';
            result.className = 'result success';
            
            let answerText = '';
            if (data.type === 'true_false') {
                answerText = data.answer ? 'صح' : 'خطأ';
            } else {
                answerText = data.options[data.answer];
            }
            
            result.innerHTML = `
                <h3>الإجابة:</h3>
                <p style="font-size: 1.5em; margin: 10px 0;">${answerText}</p>
                <button onclick="resetForm()" class="btn">سؤال جديد</button>
            `;
        }

        function showError(message) {
            timer.style.display = 'none';
            loading.style.display = 'none';
            result.style.display = 'block';
            result.className = 'result error';
            result.innerHTML = `
                <h3>خطأ:</h3>
                <p>${message}</p>
                <button onclick="resetForm()" class="btn">المحاولة مرة أخرى</button>
            `;
        }

        function resetForm() {
            questionForm.style.display = 'block';
            loading.style.display = 'none';
            timer.style.display = 'none';
            result.style.display = 'none';
            questionForm.reset();
            optionsContainer.style.display = 'none';
        }
    </script>
</body>
</html>
