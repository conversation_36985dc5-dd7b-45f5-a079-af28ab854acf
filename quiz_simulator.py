#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكي تطبيق اختبار - يحاكي تطبيق اختبار حقيقي
ويستخدم مجيب الأسئلة الذكي للحصول على الإجابات تلقائياً
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
import json
import threading
import time

class QuizSimulator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("محاكي تطبيق الاختبار")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f0f0')
        
        # الأسئلة التجريبية
        self.questions = [
            {
                "question": "الأرض كروية الشكل",
                "type": "true_false",
                "correct_answer": True
            },
            {
                "question": "ما هي عاصمة مصر؟",
                "type": "multiple_choice",
                "options": ["الإسكندرية", "القاهرة", "الجيزة", "أسوان"],
                "correct_answer": 1
            },
            {
                "question": "الشمس نجم",
                "type": "true_false", 
                "correct_answer": True
            },
            {
                "question": "كم عدد قارات العالم؟",
                "type": "multiple_choice",
                "options": ["5", "6", "7", "8"],
                "correct_answer": 2
            },
            {
                "question": "الماء يتكون من الهيدروجين والأكسجين",
                "type": "true_false",
                "correct_answer": True
            }
        ]
        
        self.current_question = 0
        self.score = 0
        self.ai_server = "http://localhost:5000"
        self.auto_answer_enabled = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root, 
            text="🎯 محاكي تطبيق الاختبار", 
            font=("Arial", 18, "bold"),
            bg='#f0f0f0',
            fg='#333'
        )
        title_label.pack(pady=20)
        
        # إعدادات الذكاء الاصطناعي
        ai_frame = tk.Frame(self.root, bg='#f0f0f0')
        ai_frame.pack(pady=10)
        
        self.ai_var = tk.BooleanVar()
        ai_checkbox = tk.Checkbutton(
            ai_frame,
            text="🤖 تفعيل الإجابة التلقائية بالذكاء الاصطناعي",
            variable=self.ai_var,
            command=self.toggle_ai,
            font=("Arial", 12),
            bg='#f0f0f0'
        )
        ai_checkbox.pack()
        
        # معلومات السؤال
        info_frame = tk.Frame(self.root, bg='#f0f0f0')
        info_frame.pack(pady=10)
        
        self.question_info = tk.Label(
            info_frame,
            text=f"السؤال 1 من {len(self.questions)}",
            font=("Arial", 12),
            bg='#f0f0f0'
        )
        self.question_info.pack()
        
        # منطقة السؤال
        question_frame = tk.Frame(self.root, bg='white', relief='raised', bd=2)
        question_frame.pack(pady=20, padx=20, fill='both', expand=True)
        
        self.question_label = tk.Label(
            question_frame,
            text="",
            font=("Arial", 14),
            bg='white',
            wraplength=500,
            justify='center'
        )
        self.question_label.pack(pady=20)
        
        # منطقة الإجابات
        self.answer_frame = tk.Frame(question_frame, bg='white')
        self.answer_frame.pack(pady=10)
        
        # أزرار التحكم
        control_frame = tk.Frame(self.root, bg='#f0f0f0')
        control_frame.pack(pady=20)
        
        self.submit_btn = tk.Button(
            control_frame,
            text="إرسال الإجابة",
            command=self.submit_answer,
            font=("Arial", 12),
            bg='#4CAF50',
            fg='white',
            padx=20
        )
        self.submit_btn.pack(side='left', padx=10)
        
        self.next_btn = tk.Button(
            control_frame,
            text="السؤال التالي",
            command=self.next_question,
            font=("Arial", 12),
            bg='#2196F3',
            fg='white',
            padx=20,
            state='disabled'
        )
        self.next_btn.pack(side='left', padx=10)
        
        # شريط الحالة
        self.status_label = tk.Label(
            self.root,
            text="جاهز للبدء",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#666'
        )
        self.status_label.pack(pady=5)
        
        # عرض السؤال الأول
        self.display_question()
        
    def toggle_ai(self):
        """تفعيل/إلغاء تفعيل الذكاء الاصطناعي"""
        self.auto_answer_enabled = self.ai_var.get()
        
        if self.auto_answer_enabled:
            self.status_label.config(text="🤖 الذكاء الاصطناعي مُفعل - سيجيب تلقائياً", fg='green')
            # بدء الإجابة التلقائية
            threading.Thread(target=self.auto_answer, daemon=True).start()
        else:
            self.status_label.config(text="الذكاء الاصطناعي مُعطل", fg='red')
    
    def display_question(self):
        """عرض السؤال الحالي"""
        if self.current_question >= len(self.questions):
            self.show_results()
            return
            
        question = self.questions[self.current_question]
        
        # تحديث معلومات السؤال
        self.question_info.config(text=f"السؤال {self.current_question + 1} من {len(self.questions)}")
        self.question_label.config(text=question["question"])
        
        # مسح الإجابات السابقة
        for widget in self.answer_frame.winfo_children():
            widget.destroy()
            
        # عرض خيارات الإجابة
        self.answer_var = tk.StringVar()
        
        if question["type"] == "true_false":
            tk.Radiobutton(
                self.answer_frame,
                text="صح",
                variable=self.answer_var,
                value="True",
                font=("Arial", 12),
                bg='white'
            ).pack(anchor='w', padx=20)
            
            tk.Radiobutton(
                self.answer_frame,
                text="خطأ",
                variable=self.answer_var,
                value="False",
                font=("Arial", 12),
                bg='white'
            ).pack(anchor='w', padx=20)
            
        else:  # multiple_choice
            for i, option in enumerate(question["options"]):
                tk.Radiobutton(
                    self.answer_frame,
                    text=f"{chr(65+i)}) {option}",
                    variable=self.answer_var,
                    value=str(i),
                    font=("Arial", 12),
                    bg='white'
                ).pack(anchor='w', padx=20)
        
        # إعادة تعيين الأزرار
        self.submit_btn.config(state='normal')
        self.next_btn.config(state='disabled')
    
    def auto_answer(self):
        """الحصول على الإجابة من الذكاء الاصطناعي"""
        if not self.auto_answer_enabled:
            return
            
        question = self.questions[self.current_question]
        
        try:
            self.status_label.config(text="🔄 جاري الحصول على الإجابة من الذكاء الاصطناعي...")
            
            # إعداد البيانات للإرسال
            data = {
                "question": question["question"],
                "type": question["type"]
            }
            
            if question["type"] == "multiple_choice":
                data["options"] = question["options"]
            
            # إرسال السؤال
            response = requests.post(f"{self.ai_server}/submit_question", json=data, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                question_id = result.get("question_id")
                
                # انتظار الإجابة
                for i in range(12):
                    time.sleep(1)
                    answer_response = requests.get(f"{self.ai_server}/get_answer/{question_id}")
                    
                    if answer_response.status_code == 200:
                        answer_data = answer_response.json()
                        
                        if answer_data.get("status") == "completed":
                            # تطبيق الإجابة
                            ai_answer = answer_data.get("answer")
                            
                            if question["type"] == "true_false":
                                self.answer_var.set("True" if ai_answer else "False")
                            else:
                                self.answer_var.set(str(ai_answer))
                            
                            self.status_label.config(text="✅ تم الحصول على الإجابة من الذكاء الاصطناعي", fg='green')
                            return
                            
                        elif answer_data.get("status") in ["timeout", "error"]:
                            break
                
                self.status_label.config(text="❌ فشل في الحصول على الإجابة", fg='red')
            else:
                self.status_label.config(text="❌ خطأ في الاتصال بالخادم", fg='red')
                
        except Exception as e:
            self.status_label.config(text=f"❌ خطأ: {str(e)}", fg='red')
    
    def submit_answer(self):
        """إرسال الإجابة"""
        if not self.answer_var.get():
            messagebox.showwarning("تحذير", "يرجى اختيار إجابة")
            return
            
        question = self.questions[self.current_question]
        user_answer = self.answer_var.get()
        
        # التحقق من صحة الإجابة
        if question["type"] == "true_false":
            correct = (user_answer == "True") == question["correct_answer"]
        else:
            correct = int(user_answer) == question["correct_answer"]
        
        if correct:
            self.score += 1
            messagebox.showinfo("صحيح! ✅", "إجابة صحيحة!")
        else:
            messagebox.showinfo("خطأ ❌", "إجابة خاطئة!")
        
        self.submit_btn.config(state='disabled')
        self.next_btn.config(state='normal')
    
    def next_question(self):
        """الانتقال للسؤال التالي"""
        self.current_question += 1
        self.display_question()
        
        # إذا كان الذكاء الاصطناعي مُفعل، احصل على الإجابة التلقائية
        if self.auto_answer_enabled:
            threading.Thread(target=self.auto_answer, daemon=True).start()
    
    def show_results(self):
        """عرض النتائج النهائية"""
        percentage = (self.score / len(self.questions)) * 100
        
        result_text = f"""
🎉 انتهى الاختبار!

النتيجة: {self.score} من {len(self.questions)}
النسبة المئوية: {percentage:.1f}%

{'ممتاز! 🌟' if percentage >= 80 else 'جيد! 👍' if percentage >= 60 else 'يحتاج تحسين 📚'}
        """
        
        messagebox.showinfo("النتائج", result_text)
        self.root.quit()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """تشغيل محاكي الاختبار"""
    print("🎯 بدء تشغيل محاكي تطبيق الاختبار...")
    print("تأكد من تشغيل مجيب الأسئلة الذكي على http://localhost:5000")
    
    app = QuizSimulator()
    app.run()

if __name__ == "__main__":
    main()
