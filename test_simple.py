#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتأكد من عمل التطبيق
"""

import requests
import time

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    server_url = "http://localhost:5000"
    
    print("🧪 اختبار الوظائف الأساسية...")
    print("=" * 40)
    
    # اختبار 1: التحقق من أن الخادم يعمل
    try:
        response = requests.get(server_url, timeout=5)
        if response.status_code == 200:
            print("✅ الخادم يعمل بشكل صحيح")
        else:
            print(f"❌ خطأ في الخادم: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ لا يمكن الوصول للخادم: {e}")
        print("💡 تأكد من تشغيل التطبيق: py app.py")
        return False
    
    # اختبار 2: إرسال سؤال بسيط
    try:
        print("\n🔄 اختبار إرسال سؤال...")
        
        question_data = {
            "question": "2 + 2 = 4",
            "type": "true_false",
            "options": []
        }
        
        response = requests.post(f"{server_url}/submit_question", 
                               json=question_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            question_id = result.get("question_id")
            print(f"✅ تم إرسال السؤال بنجاح (ID: {question_id})")
            
            # انتظار الإجابة
            print("⏳ انتظار الإجابة...")
            for i in range(12):
                time.sleep(1)
                answer_response = requests.get(f"{server_url}/get_answer/{question_id}")
                
                if answer_response.status_code == 200:
                    answer_data = answer_response.json()
                    
                    if answer_data.get("status") == "completed":
                        answer = "صح" if answer_data.get("answer") else "خطأ"
                        print(f"✅ الإجابة: {answer}")
                        return True
                    elif answer_data.get("status") in ["timeout", "error"]:
                        print(f"❌ خطأ في الإجابة: {answer_data.get('message')}")
                        return False
            
            print("❌ انتهى الوقت المحدد")
            return False
        else:
            print(f"❌ خطأ في إرسال السؤال: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_monitor_status():
    """اختبار حالة المراقبة"""
    server_url = "http://localhost:5000"
    
    print("\n🔍 اختبار حالة المراقبة...")
    print("=" * 40)
    
    try:
        response = requests.get(f"{server_url}/monitor_status", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"📋 مكتبة الحافظة: {'✅ متاحة' if data.get('clipboard_available') else '❌ غير متاحة'}")
            print(f"🖼️ مكتبات OCR: {'✅ متاحة' if data.get('ocr_available') else '❌ غير متاحة'}")
            print(f"🔄 مراقبة الحافظة: {'🟢 نشط' if data.get('clipboard_active') else '🔴 غير نشط'}")
            
            if not data.get('clipboard_available'):
                print("\n💡 لتفعيل مراقبة الحافظة:")
                print("   py -m pip install pyperclip")
            
            if not data.get('ocr_available'):
                print("\n💡 لتفعيل تحليل الصور:")
                print("   py -m pip install opencv-python pillow pytesseract numpy")
            
            return True
        else:
            print(f"❌ خطأ في فحص الحالة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص الحالة: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار التطبيق...")
    print("تأكد من تشغيل التطبيق أولاً: py app.py")
    print()
    
    # اختبار الوظائف الأساسية
    basic_test = test_basic_functionality()
    
    # اختبار حالة المراقبة
    monitor_test = test_monitor_status()
    
    print("\n" + "=" * 40)
    print("📊 نتائج الاختبار:")
    print(f"   الوظائف الأساسية: {'✅ نجح' if basic_test else '❌ فشل'}")
    print(f"   حالة المراقبة: {'✅ نجح' if monitor_test else '❌ فشل'}")
    
    if basic_test and monitor_test:
        print("\n🎉 جميع الاختبارات نجحت! التطبيق يعمل بشكل مثالي!")
        print("\n🔗 الروابط المتاحة:")
        print("   الصفحة الرئيسية: http://localhost:5000")
        print("   صفحة المراقبة: http://localhost:5000/monitor")
        print("   صفحة الاختبار: http://localhost:5000/test")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى حل")
        print("💡 تأكد من:")
        print("   1. تشغيل التطبيق: py app.py")
        print("   2. تثبيت المكتبات: py -m pip install flask requests werkzeug")
        print("   3. فتح المنفذ 5000")

if __name__ == "__main__":
    main()
