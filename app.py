from flask import Flask, render_template, request, jsonify
import requests
import json
import time
import threading
from datetime import datetime

app = Flask(__name__)

# قاموس لتخزين الأسئلة والإجابات المؤقتة
questions_storage = {}

class QuestionAnswerer:
    def __init__(self):
        # استخدام Hugging Face API المجاني
        self.api_url = "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium"
        # يمكنك وضع مفتاح API هنا للحصول على إجابات أفضل (اختياري)
        self.headers = {"Authorization": "Bearer hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"}
        self.use_api = False  # تعيين False لاستخدام النظام الاحتياطي فقط
        
    def get_ai_answer(self, question, options=None):
        """الحصول على إجابة من الذكاء الاصطناعي"""
        # استخدام النظام الاحتياطي مباشرة إذا لم يتم تفعيل API
        if not self.use_api:
            return self.get_fallback_answer(question, options)

        try:
            # إعداد النص للذكاء الاصطناعي
            if options:
                prompt = f"Question: {question}\nOptions: {', '.join(options)}\nAnswer with only the letter or option:"
            else:
                prompt = f"True or False: {question}\nAnswer with only 'True' or 'False':"

            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_length": 50,
                    "temperature": 0.3
                }
            }

            response = requests.post(self.api_url, headers=self.headers, json=payload, timeout=8)

            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    answer = result[0].get('generated_text', '').strip()
                    return self.parse_answer(answer, options)

            # إجابة افتراضية في حالة فشل API
            return self.get_fallback_answer(question, options)

        except Exception as e:
            print(f"خطأ في API: {e}")
            return self.get_fallback_answer(question, options)
    
    def parse_answer(self, ai_response, options):
        """تحليل إجابة الذكاء الاصطناعي"""
        ai_response = ai_response.lower().strip()
        
        if options:
            # للأسئلة متعددة الخيارات
            for i, option in enumerate(options):
                if str(i+1) in ai_response or option.lower() in ai_response:
                    return i
            return 0  # الخيار الأول كافتراضي
        else:
            # لأسئلة صح/خطأ
            if 'true' in ai_response or 'صح' in ai_response or 'نعم' in ai_response:
                return True
            else:
                return False
    
    def get_fallback_answer(self, question, options):
        """نظام إجابة احتياطي ذكي"""
        import random

        if options:
            # للأسئلة متعددة الخيارات - تحليل ذكي
            question_lower = question.lower()

            # البحث عن كلمات مفتاحية في الخيارات
            for i, option in enumerate(options):
                option_lower = option.lower()
                # إذا كان الخيار يحتوي على كلمات من السؤال
                question_words = question_lower.split()
                option_words = option_lower.split()
                common_words = set(question_words) & set(option_words)
                if len(common_words) > 0:
                    return i

            # أسئلة جغرافية شائعة
            if 'عاصمة' in question or 'capital' in question_lower:
                capitals = ['باريس', 'لندن', 'برلين', 'روما', 'مدريد', 'paris', 'london', 'berlin', 'rome']
                for i, option in enumerate(options):
                    if any(cap in option.lower() for cap in capitals):
                        return i

            # اختيار عشوائي كحل أخير
            return random.randint(0, len(options)-1)
        else:
            # لأسئلة صح/خطأ - تحليل ذكي
            question_lower = question.lower()

            # كلمات إيجابية تشير إلى "صح"
            positive_indicators = [
                'is', 'are', 'can', 'will', 'does', 'has', 'have',
                'هل', 'يمكن', 'هي', 'هو', 'تعتبر', 'يعتبر',
                'correct', 'true', 'right', 'yes',
                'الأرض', 'الشمس', 'الماء', 'الهواء'
            ]

            # كلمات سلبية تشير إلى "خطأ"
            negative_indicators = [
                'not', 'never', 'cannot', 'impossible', 'false', 'wrong',
                'لا', 'ليس', 'غير', 'مستحيل', 'خطأ', 'خاطئ'
            ]

            # حساب النقاط
            positive_score = sum(1 for word in positive_indicators if word in question_lower)
            negative_score = sum(1 for word in negative_indicators if word in question_lower)

            # أسئلة رياضية بسيطة
            if any(op in question for op in ['+', '-', '*', '/', '=', '٢', '٣', '٤', '٥']):
                # محاولة تقييم العمليات الرياضية البسيطة
                try:
                    if '=' in question:
                        parts = question.split('=')
                        if len(parts) == 2:
                            left = parts[0].strip().replace('×', '*').replace('÷', '/')
                            right = parts[1].strip()
                            # تقييم بسيط للعمليات الأساسية
                            if all(c in '0123456789+-*/ ().' for c in left):
                                try:
                                    result = eval(left)
                                    expected = float(right)
                                    return abs(result - expected) < 0.01
                                except:
                                    pass
                except:
                    pass

            # حقائق علمية شائعة
            science_facts = {
                'الأرض كروية': True,
                'الشمس نجم': True,
                'الماء يتكون من الهيدروجين والأكسجين': True,
                'earth is round': True,
                'sun is a star': True,
                'water is h2o': True,
                '2+2=4': True,
                '2+2=5': False
            }

            for fact, answer in science_facts.items():
                if fact.lower() in question_lower:
                    return answer

            # إذا كان هناك المزيد من الكلمات الإيجابية
            if positive_score > negative_score:
                return True
            elif negative_score > positive_score:
                return False
            else:
                # اختيار عشوائي كحل أخير
                return random.choice([True, False])

answerer = QuestionAnswerer()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/submit_question', methods=['POST'])
def submit_question():
    data = request.json
    question_text = data.get('question', '')
    question_type = data.get('type', 'true_false')
    options = data.get('options', [])
    
    # إنشاء معرف فريد للسؤال
    question_id = str(int(time.time() * 1000))
    
    # تخزين السؤال
    questions_storage[question_id] = {
        'question': question_text,
        'type': question_type,
        'options': options,
        'status': 'processing',
        'answer': None,
        'timestamp': datetime.now()
    }
    
    # بدء معالجة السؤال في خيط منفصل
    thread = threading.Thread(target=process_question, args=(question_id,))
    thread.start()
    
    return jsonify({'question_id': question_id, 'status': 'submitted'})

def process_question(question_id):
    """معالجة السؤال والحصول على الإجابة"""
    try:
        question_data = questions_storage[question_id]
        
        # الحصول على الإجابة من الذكاء الاصطناعي
        answer = answerer.get_ai_answer(
            question_data['question'],
            question_data['options'] if question_data['type'] == 'multiple_choice' else None
        )
        
        # تحديث البيانات
        questions_storage[question_id]['answer'] = answer
        questions_storage[question_id]['status'] = 'completed'
        
    except Exception as e:
        print(f"خطأ في معالجة السؤال {question_id}: {e}")
        questions_storage[question_id]['status'] = 'error'

@app.route('/get_answer/<question_id>')
def get_answer(question_id):
    if question_id in questions_storage:
        question_data = questions_storage[question_id]
        
        # التحقق من انتهاء الوقت (10 ثوانٍ)
        time_elapsed = (datetime.now() - question_data['timestamp']).total_seconds()
        
        if time_elapsed > 10:
            return jsonify({
                'status': 'timeout',
                'message': 'انتهى الوقت المحدد (10 ثوانٍ)'
            })
        
        if question_data['status'] == 'completed':
            return jsonify({
                'status': 'completed',
                'answer': question_data['answer'],
                'question': question_data['question'],
                'type': question_data['type'],
                'options': question_data['options']
            })
        elif question_data['status'] == 'error':
            return jsonify({
                'status': 'error',
                'message': 'حدث خطأ في معالجة السؤال'
            })
        else:
            return jsonify({
                'status': 'processing',
                'time_remaining': max(0, 10 - time_elapsed)
            })
    
    return jsonify({'status': 'not_found', 'message': 'السؤال غير موجود'})

@app.route('/test')
def test():
    return render_template('test.html')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
