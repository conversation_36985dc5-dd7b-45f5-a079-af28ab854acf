from flask import Flask, render_template, request, jsonify
import requests
import json
import time
import threading
from datetime import datetime
import os
import re

# استيراد المكتبات الاختيارية
try:
    import pyperclip
    CLIPBOARD_AVAILABLE = True
except ImportError:
    CLIPBOARD_AVAILABLE = False
    print("تحذير: pyperclip غير مثبت - مراقبة الحافظة غير متاحة")

try:
    import cv2
    import numpy as np
    from PIL import Image, ImageGrab
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("تحذير: مكتبات OCR غير مثبتة - تحليل الصور غير متاح")

app = Flask(__name__)

# قاموس لتخزين الأسئلة والإجابات المؤقتة
questions_storage = {}

# متغيرات لمراقبة التطبيقات الأخرى
clipboard_monitor_active = False
screen_monitor_active = False
last_clipboard_content = ""
auto_answer_enabled = False

class QuestionAnswerer:
    def __init__(self):
        # استخدام Hugging Face API المجاني
        self.api_url = "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium"
        # يمكنك وضع مفتاح API هنا للحصول على إجابات أفضل (اختياري)
        self.headers = {"Authorization": "Bearer hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"}
        self.use_api = False  # تعيين False لاستخدام النظام الاحتياطي فقط
        
    def get_ai_answer(self, question, options=None):
        """الحصول على إجابة من الذكاء الاصطناعي"""
        # استخدام النظام الاحتياطي مباشرة إذا لم يتم تفعيل API
        if not self.use_api:
            return self.get_fallback_answer(question, options)

        try:
            # إعداد النص للذكاء الاصطناعي
            if options:
                prompt = f"Question: {question}\nOptions: {', '.join(options)}\nAnswer with only the letter or option:"
            else:
                prompt = f"True or False: {question}\nAnswer with only 'True' or 'False':"

            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_length": 50,
                    "temperature": 0.3
                }
            }

            response = requests.post(self.api_url, headers=self.headers, json=payload, timeout=8)

            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    answer = result[0].get('generated_text', '').strip()
                    return self.parse_answer(answer, options)

            # إجابة افتراضية في حالة فشل API
            return self.get_fallback_answer(question, options)

        except Exception as e:
            print(f"خطأ في API: {e}")
            return self.get_fallback_answer(question, options)
    
    def parse_answer(self, ai_response, options):
        """تحليل إجابة الذكاء الاصطناعي"""
        ai_response = ai_response.lower().strip()
        
        if options:
            # للأسئلة متعددة الخيارات
            for i, option in enumerate(options):
                if str(i+1) in ai_response or option.lower() in ai_response:
                    return i
            return 0  # الخيار الأول كافتراضي
        else:
            # لأسئلة صح/خطأ
            if 'true' in ai_response or 'صح' in ai_response or 'نعم' in ai_response:
                return True
            else:
                return False
    
    def get_fallback_answer(self, question, options):
        """نظام إجابة احتياطي ذكي"""
        import random

        if options:
            # للأسئلة متعددة الخيارات - تحليل ذكي
            question_lower = question.lower()

            # البحث عن كلمات مفتاحية في الخيارات
            for i, option in enumerate(options):
                option_lower = option.lower()
                # إذا كان الخيار يحتوي على كلمات من السؤال
                question_words = question_lower.split()
                option_words = option_lower.split()
                common_words = set(question_words) & set(option_words)
                if len(common_words) > 0:
                    return i

            # أسئلة جغرافية شائعة
            if 'عاصمة' in question or 'capital' in question_lower:
                capitals = ['باريس', 'لندن', 'برلين', 'روما', 'مدريد', 'paris', 'london', 'berlin', 'rome']
                for i, option in enumerate(options):
                    if any(cap in option.lower() for cap in capitals):
                        return i

            # اختيار عشوائي كحل أخير
            return random.randint(0, len(options)-1)
        else:
            # لأسئلة صح/خطأ - تحليل ذكي
            question_lower = question.lower()

            # كلمات إيجابية تشير إلى "صح"
            positive_indicators = [
                'is', 'are', 'can', 'will', 'does', 'has', 'have',
                'هل', 'يمكن', 'هي', 'هو', 'تعتبر', 'يعتبر',
                'correct', 'true', 'right', 'yes',
                'الأرض', 'الشمس', 'الماء', 'الهواء'
            ]

            # كلمات سلبية تشير إلى "خطأ"
            negative_indicators = [
                'not', 'never', 'cannot', 'impossible', 'false', 'wrong',
                'لا', 'ليس', 'غير', 'مستحيل', 'خطأ', 'خاطئ'
            ]

            # حساب النقاط
            positive_score = sum(1 for word in positive_indicators if word in question_lower)
            negative_score = sum(1 for word in negative_indicators if word in question_lower)

            # أسئلة رياضية بسيطة
            if any(op in question for op in ['+', '-', '*', '/', '=', '٢', '٣', '٤', '٥']):
                # محاولة تقييم العمليات الرياضية البسيطة
                try:
                    if '=' in question:
                        parts = question.split('=')
                        if len(parts) == 2:
                            left = parts[0].strip().replace('×', '*').replace('÷', '/')
                            right = parts[1].strip()
                            # تقييم بسيط للعمليات الأساسية
                            if all(c in '0123456789+-*/ ().' for c in left):
                                try:
                                    result = eval(left)
                                    expected = float(right)
                                    return abs(result - expected) < 0.01
                                except:
                                    pass
                except:
                    pass

            # حقائق علمية شائعة
            science_facts = {
                'الأرض كروية': True,
                'الشمس نجم': True,
                'الماء يتكون من الهيدروجين والأكسجين': True,
                'earth is round': True,
                'sun is a star': True,
                'water is h2o': True,
                '2+2=4': True,
                '2+2=5': False
            }

            for fact, answer in science_facts.items():
                if fact.lower() in question_lower:
                    return answer

            # إذا كان هناك المزيد من الكلمات الإيجابية
            if positive_score > negative_score:
                return True
            elif negative_score > positive_score:
                return False
            else:
                # اختيار عشوائي كحل أخير
                return random.choice([True, False])

class ExternalAppMonitor:
    def __init__(self, answerer):
        self.answerer = answerer
        self.is_monitoring = False

    def extract_text_from_screen(self, region=None):
        """استخراج النص من الشاشة باستخدام OCR"""
        if not OCR_AVAILABLE:
            return "OCR غير متاح - يرجى تثبيت المكتبات المطلوبة"

        try:
            # التقاط الشاشة
            screenshot = ImageGrab.grab(bbox=region)

            # تحويل إلى نص باستخدام Tesseract
            # تأكد من تثبيت Tesseract: https://github.com/UB-Mannheim/tesseract/wiki
            text = pytesseract.image_to_string(screenshot, lang='ara+eng')
            return text.strip()
        except Exception as e:
            print(f"خطأ في استخراج النص من الشاشة: {e}")
            return ""

    def monitor_clipboard(self):
        """مراقبة الحافظة للأسئلة الجديدة"""
        global last_clipboard_content, clipboard_monitor_active

        if not CLIPBOARD_AVAILABLE:
            print("خطأ: مكتبة pyperclip غير مثبتة")
            return

        while clipboard_monitor_active:
            try:
                current_content = pyperclip.paste()

                if current_content != last_clipboard_content and current_content.strip():
                    last_clipboard_content = current_content

                    # تحليل المحتوى للبحث عن أسئلة
                    question_data = self.parse_question_from_text(current_content)

                    if question_data:
                        print(f"تم اكتشاف سؤال جديد: {question_data['question']}")

                        # الحصول على الإجابة
                        answer = self.answerer.get_ai_answer(
                            question_data['question'],
                            question_data.get('options')
                        )

                        # نسخ الإجابة إلى الحافظة
                        if question_data['type'] == 'true_false':
                            answer_text = 'صح' if answer else 'خطأ'
                        else:
                            answer_text = question_data['options'][answer] if question_data.get('options') else str(answer)

                        pyperclip.copy(answer_text)
                        print(f"تم نسخ الإجابة إلى الحافظة: {answer_text}")

                time.sleep(1)  # فحص كل ثانية

            except Exception as e:
                print(f"خطأ في مراقبة الحافظة: {e}")
                time.sleep(2)

    def parse_question_from_text(self, text):
        """تحليل النص لاستخراج الأسئلة"""
        text = text.strip()

        # البحث عن أسئلة صح/خطأ
        true_false_patterns = [
            r'.*\?.*صح.*خطأ',
            r'.*\?.*True.*False',
            r'.*\?.*نعم.*لا',
            r'صح أم خطأ.*\?',
            r'True or False.*\?'
        ]

        for pattern in true_false_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                question = re.sub(r'(صح|خطأ|True|False|نعم|لا)', '', text).strip()
                question = re.sub(r'[؟?].*', '?', question)
                return {
                    'question': question,
                    'type': 'true_false',
                    'options': None
                }

        # البحث عن أسئلة متعددة الخيارات
        lines = text.split('\n')
        question_line = ""
        options = []

        for i, line in enumerate(lines):
            line = line.strip()
            if '?' in line or '؟' in line:
                question_line = line
                # البحث عن الخيارات في الأسطر التالية
                for j in range(i+1, min(i+5, len(lines))):
                    option_line = lines[j].strip()
                    if option_line and (
                        option_line.startswith(('A)', 'B)', 'C)', 'D)', 'أ)', 'ب)', 'ج)', 'د)')) or
                        option_line.startswith(('1.', '2.', '3.', '4.', '١.', '٢.', '٣.', '٤.'))
                    ):
                        # إزالة الرقم أو الحرف من بداية الخيار
                        clean_option = re.sub(r'^[A-Dأ-د1-4١-٤][.)]\s*', '', option_line)
                        options.append(clean_option)
                break

        if question_line and len(options) >= 2:
            return {
                'question': question_line,
                'type': 'multiple_choice',
                'options': options
            }

        # إذا لم يتم العثور على نمط محدد، اعتبره سؤال صح/خطأ
        if '?' in text or '؟' in text:
            return {
                'question': text,
                'type': 'true_false',
                'options': None
            }

        return None

    def start_clipboard_monitoring(self):
        """بدء مراقبة الحافظة"""
        if not CLIPBOARD_AVAILABLE:
            return False

        global clipboard_monitor_active
        clipboard_monitor_active = True

        monitor_thread = threading.Thread(target=self.monitor_clipboard)
        monitor_thread.daemon = True
        monitor_thread.start()

        return True

    def stop_clipboard_monitoring(self):
        """إيقاف مراقبة الحافظة"""
        global clipboard_monitor_active
        clipboard_monitor_active = False
        return True

answerer = QuestionAnswerer()
external_monitor = ExternalAppMonitor(answerer)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/submit_question', methods=['POST'])
def submit_question():
    data = request.json
    question_text = data.get('question', '')
    question_type = data.get('type', 'true_false')
    options = data.get('options', [])
    
    # إنشاء معرف فريد للسؤال
    question_id = str(int(time.time() * 1000))
    
    # تخزين السؤال
    questions_storage[question_id] = {
        'question': question_text,
        'type': question_type,
        'options': options,
        'status': 'processing',
        'answer': None,
        'timestamp': datetime.now()
    }
    
    # بدء معالجة السؤال في خيط منفصل
    thread = threading.Thread(target=process_question, args=(question_id,))
    thread.start()
    
    return jsonify({'question_id': question_id, 'status': 'submitted'})

def process_question(question_id):
    """معالجة السؤال والحصول على الإجابة"""
    try:
        question_data = questions_storage[question_id]
        
        # الحصول على الإجابة من الذكاء الاصطناعي
        answer = answerer.get_ai_answer(
            question_data['question'],
            question_data['options'] if question_data['type'] == 'multiple_choice' else None
        )
        
        # تحديث البيانات
        questions_storage[question_id]['answer'] = answer
        questions_storage[question_id]['status'] = 'completed'
        
    except Exception as e:
        print(f"خطأ في معالجة السؤال {question_id}: {e}")
        questions_storage[question_id]['status'] = 'error'

@app.route('/get_answer/<question_id>')
def get_answer(question_id):
    if question_id in questions_storage:
        question_data = questions_storage[question_id]
        
        # التحقق من انتهاء الوقت (10 ثوانٍ)
        time_elapsed = (datetime.now() - question_data['timestamp']).total_seconds()
        
        if time_elapsed > 10:
            return jsonify({
                'status': 'timeout',
                'message': 'انتهى الوقت المحدد (10 ثوانٍ)'
            })
        
        if question_data['status'] == 'completed':
            return jsonify({
                'status': 'completed',
                'answer': question_data['answer'],
                'question': question_data['question'],
                'type': question_data['type'],
                'options': question_data['options']
            })
        elif question_data['status'] == 'error':
            return jsonify({
                'status': 'error',
                'message': 'حدث خطأ في معالجة السؤال'
            })
        else:
            return jsonify({
                'status': 'processing',
                'time_remaining': max(0, 10 - time_elapsed)
            })
    
    return jsonify({'status': 'not_found', 'message': 'السؤال غير موجود'})

@app.route('/test')
def test():
    return render_template('test.html')

@app.route('/monitor')
def monitor():
    return render_template('monitor.html')

@app.route('/start_clipboard_monitor', methods=['POST'])
def start_clipboard_monitor():
    """بدء مراقبة الحافظة"""
    try:
        success = external_monitor.start_clipboard_monitoring()
        return jsonify({
            'status': 'success' if success else 'error',
            'message': 'تم بدء مراقبة الحافظة' if success else 'فشل في بدء المراقبة'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ: {str(e)}'
        })

@app.route('/stop_clipboard_monitor', methods=['POST'])
def stop_clipboard_monitor():
    """إيقاف مراقبة الحافظة"""
    try:
        success = external_monitor.stop_clipboard_monitoring()
        return jsonify({
            'status': 'success' if success else 'error',
            'message': 'تم إيقاف مراقبة الحافظة' if success else 'فشل في إيقاف المراقبة'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ: {str(e)}'
        })

@app.route('/monitor_status')
def monitor_status():
    """حالة المراقبة"""
    global clipboard_monitor_active
    return jsonify({
        'clipboard_active': clipboard_monitor_active,
        'clipboard_available': CLIPBOARD_AVAILABLE,
        'ocr_available': OCR_AVAILABLE,
        'last_clipboard': last_clipboard_content[:100] + '...' if len(last_clipboard_content) > 100 else last_clipboard_content
    })

@app.route('/process_external_question', methods=['POST'])
def process_external_question():
    """معالجة سؤال من تطبيق خارجي"""
    try:
        data = request.json
        text = data.get('text', '')

        if not text:
            return jsonify({'status': 'error', 'message': 'لم يتم إرسال نص'})

        # تحليل النص
        question_data = external_monitor.parse_question_from_text(text)

        if not question_data:
            return jsonify({'status': 'error', 'message': 'لم يتم العثور على سؤال في النص'})

        # الحصول على الإجابة
        answer = answerer.get_ai_answer(
            question_data['question'],
            question_data.get('options')
        )

        # تنسيق الإجابة
        if question_data['type'] == 'true_false':
            answer_text = 'صح' if answer else 'خطأ'
        else:
            answer_text = question_data['options'][answer] if question_data.get('options') else str(answer)

        return jsonify({
            'status': 'success',
            'question': question_data['question'],
            'type': question_data['type'],
            'options': question_data.get('options'),
            'answer': answer,
            'answer_text': answer_text
        })

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'خطأ في معالجة السؤال: {str(e)}'
        })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
