# 🚀 دليل البدء السريع

## التشغيل السريع

### 1. تشغيل التطبيق الأساسي
```bash
python app.py
```
أو
```bash
run.bat
```

### 2. فتح المتصفح
اذهب إلى: `http://localhost:5000`

## 🔥 الإجابة على أسئلة التطبيقات الأخرى

### الطريقة الأولى: مراقبة الحافظة (الأسهل)

1. **اذهب إلى صفحة المراقبة**: `http://localhost:5000/monitor`
2. **اضغط "بدء مراقبة الحافظة"**
3. **في التطبيق الآخر**: انسخ السؤال (Ctrl+C)
4. **ارجع للتطبيق الآخر**: الصق الإجابة (Ctrl+V)

### الطريقة الثانية: محاكي الاختبار

```bash
python quiz_simulator.py
```

- ✅ فعّل "الإجابة التلقائية بالذكاء الاصطناعي"
- ✅ شاهد كيف يجيب التطبيق تلقائياً

### الطريقة الثالثة: اختبار التكامل

```bash
python test_integration.py
```

## 📋 أمثلة الأسئلة المدعومة

### صح/خطأ:
- "الأرض كروية؟"
- "الشمس نجم؟ صح أم خطأ"
- "2+2=4 True or False"

### متعدد الخيارات:
```
ما هي عاصمة فرنسا؟
A) لندن
B) باريس
C) برلين
D) روما
```

## ⚡ نصائح سريعة

1. **للاستخدام الأساسي**: لا تحتاج مكتبات إضافية
2. **لمراقبة الحافظة**: `pip install pyperclip`
3. **لتحليل الصور**: `pip install opencv-python pillow pytesseract numpy`
4. **للاختبار**: استخدم صفحة `/test`

## 🛠️ استكشاف الأخطاء

### المشكلة: التطبيق لا يعمل
```bash
pip install flask requests werkzeug
python app.py
```

### المشكلة: مراقبة الحافظة لا تعمل
```bash
pip install pyperclip
```

### المشكلة: الإجابات غير دقيقة
- أعد صياغة السؤال بوضوح
- استخدم الكلمات المفتاحية الواضحة

## 🎯 الاستخدام المتقدم

### للمطورين:
```python
import requests

# إرسال سؤال
response = requests.post('http://localhost:5000/process_external_question', 
                        json={'text': 'سؤالك هنا'})
print(response.json())
```

### للمستخدمين:
- استخدم صفحة المراقبة للتحكم الكامل
- جرب محاكي الاختبار لفهم كيفية العمل

---

**🎉 مبروك! تطبيقك جاهز للإجابة على أسئلة أي تطبيق آخر!**
